'''
Description: 南京市档案馆工作动态爬虫配置文件
Date: 2025-07-17
Author: <PERSON><PERSON><PERSON>
LastEditTime: 2025-07-17
FilePath: \Spider_faxuan\config_news_8_1.py
'''
# -*- coding: utf-8 -*-
"""
南京市档案馆工作动态分页爬虫配置文件
"""

# 起始页面配置
START_URL = "https://dag.nanjing.gov.cn/gzdt/index.html"
BASE_URL = "https://dag.nanjing.gov.cn"

# XPath配置
LIST_CONTAINER_XPATH = "//ul"  # 新闻列表容器XPath
NEWS_ITEM_XPATH = "//li"  # 新闻项XPath（所有li元素）
NEWS_TITLE_XPATH = ".//a[contains(@href, '.html')]"  # 新闻标题链接XPath（html链接）
NEWS_DATE_XPATH = ".//text()[contains(., '2025-')]"  # 新闻日期XPath（包含2025-的文本）
NEXT_PAGE_XPATH = "//a[contains(text(), '下一页')]/@href"  # 下一页链接XPath

# 分页URL模式配置
PAGE_URL_PATTERN = "https://dag.nanjing.gov.cn/gzdt/index_{}.html"  # 分页URL模式，{}为页码占位符
FIRST_PAGE_URL = "https://dag.nanjing.gov.cn/gzdt/index.html"  # 第一页URL

# 爬取配置
MAX_PAGES = 15  # 最多爬取页数（增加页数以确保覆盖5-7月的所有新闻）
START_PAGE = 0  # 起始页码

# 目标日期范围配置
TARGET_DATE_RANGE = ['2025-05', '2025-06', '2025-07']  # 爬取5-7月的新闻

# 输出文件配置
OUTPUT_FILENAME = "南京档案馆_工作动态_5-7月.xlsx"

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# 请求超时设置（秒）
REQUEST_TIMEOUT = 30

# 重试配置
MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 2  # 重试间隔（秒）

# SSL证书验证
VERIFY_SSL = True

# 代理设置
PROXIES = None

# 内容提取XPath配置（用于详情页）
CONTENT_XPATHS = [
    "//body//text()[not(ancestor::script or ancestor::style or ancestor::head)][string-length(normalize-space(.)) > 30]",  # 排除脚本、样式、头部的长文本
    "//*[not(self::script or self::style or self::head)]/text()[string-length(normalize-space(.)) > 50]",  # 排除脚本和样式的长文本
    "//text()[string-length(normalize-space(.)) > 50]",  # 长文本节点
    "//body//p[string-length(text()) > 20]",  # 长度超过20字符的段落
    "//div[contains(@class, 'content')]//text()",  # 正文内容
    "//body//div[string-length(text()) > 50]",  # 长度超过50字符的div
]

# 标题提取XPath配置
TITLE_XPATHS = [
    "//h1",
    "//h2",
    "//h3",
    "//title",
    "//div[@class='title']",
    "//div[contains(@class, 'title')]",
    "//div[contains(@class, 'content')]//h1",
    "//div[contains(@class, 'content')]//h2"
]

# 发表时间提取XPath配置
PUBLISH_TIME_XPATHS = [
    "//*[contains(text(), '发布时间：')]",  # 包含发布时间的元素
    "//text()[contains(., '发布时间：')]",  # 发布时间文本节点
    "//text()[contains(., '2025-')]",  # 包含2025-的文本节点
    "//span[contains(text(), '2025')]",  # 包含2025的span
    "//div[contains(text(), '2025')]",  # 包含2025的div
    "//p[contains(text(), '2025')]",  # 包含2025的段落
]
