'''
Description: 南京市社会工作部新闻爬虫配置文件  治理创新观察
Date: 2025-07-17
Author: <PERSON><PERSON><PERSON>
LastEditTime: 2025-07-17 17:37:01
FilePath: \Spider_faxuan\config_news_5_2.py
'''
# -*- coding: utf-8 -*-
"""
南京市社会工作部新闻分页爬虫配置文件 --治理创新观察
"""

# 起始页面配置
START_URL = "https://shgz.nanjing.gov.cn/jczl/zlcxgc/index_1.html"
BASE_URL = "https://shgz.nanjing.gov.cn"
CURRENT_URL = "https://shgz.nanjing.gov.cn/jczl/zlcxgc"

# XPath配置
LIST_CONTAINER_XPATH = "//ul"  # 新闻列表容器XPath
NEWS_ITEM_XPATH = "//ul/li[contains(., '2025-')]"  # 新闻项XPath（包含日期的li元素）
NEWS_TITLE_XPATH = ".//a[contains(@href, '.html')]"  # 新闻标题链接XPath（只选择html链接）
NEWS_DATE_XPATH = ".//text()[contains(., '2025-')]"  # 新闻日期XPath（包含2025-的文本）
NEXT_PAGE_XPATH = "//a[contains(text(), '下一页')]/@href"  # 下一页链接XPath

# 分页URL模式配置
PAGE_URL_PATTERN = "https://shgz.nanjing.gov.cn/jczl/zlcxgc/index_{}.html"  # 分页URL模式，{}为页码占位符
FIRST_PAGE_URL = "https://shgz.nanjing.gov.cn/jczl/zlcxgc/index.html"  # 第一页URL

# 爬取配置
MAX_PAGES = 15  # 最多爬取页数（增加页数以确保覆盖5-7月的所有新闻）
START_PAGE = 0  # 起始页码

# 目标日期范围配置
TARGET_DATE_RANGE = ['2025-05', '2025-06', '2025-07']  # 爬取5-7月的新闻

# 输出文件配置
OUTPUT_FILENAME = "南京社会工作部_治理创新观察_5-7月.xlsx"

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
}

# 请求超时设置（秒）
REQUEST_TIMEOUT = 30

# 重试配置
MAX_RETRIES = 3  # 最大重试次数
RETRY_DELAY = 2  # 重试间隔（秒）

# SSL证书验证
VERIFY_SSL = True

# 代理设置
PROXIES = {'http': None, 'https': None}

# 内容提取XPath配置（用于详情页）
CONTENT_XPATHS = [
    "//*[not(self::script or self::style)]/text()[string-length(normalize-space(.)) > 50]",  # 排除脚本和样式的长文本（最有效）
    "//text()[string-length(normalize-space(.)) > 50]",  # 长文本节点
    "//body//p[string-length(text()) > 20]",  # 长度超过20字符的段落
    "//div[contains(@class, 'content')]//p",  # 正文段落
    "//div[@class='main_content']//p",  # 主要内容段落
    "//body//div[string-length(text()) > 50]",  # 长度超过50字符的div
]

# 标题提取XPath配置
TITLE_XPATHS = [
    "//h1",
    "//h2",
    "//h3",
    "//title",
    "//div[@class='title']",
    "//div[contains(@class, 'title')]",
    "//div[contains(@class, 'content')]//h1",
    "//div[contains(@class, 'content')]//h2"
]

# 发表时间提取XPath配置
PUBLISH_TIME_XPATHS = [
    "//text()[contains(., '2025-')]",  # 包含2025-的文本节点
    "//span[contains(text(), '2025')]",  # 包含2025的span
    "//div[contains(text(), '2025')]",  # 包含2025的div
    "//p[contains(text(), '2025')]",  # 包含2025的段落
    "//text()[contains(., '来源：')]/following-sibling::text()[contains(., '2025')]",  # 来源后的2025文本
    "//*[contains(text(), '来源：')]",  # 包含来源的元素
]
